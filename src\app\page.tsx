import type { Metadata } from "next";
import HomePage from "@/modules/hero/templates/home";
import { JsonLd, WebPage, WebSite } from "@/modules/seo/json-ld";

export const metadata: Metadata = {
  title: "Student Loan Calculator UK - Understand Your Repayments",
  description: "Trusted tools, expert guides, and calculators to understand UK student loan repayments. Compare Plan 1, 2, 4, 5 and Postgraduate loans.",
  applicationName: "Student Loan Calculator UK",
  authors: [{ name: "Student Loan Calculator UK Team" }],
  generator: "Next.js",
  keywords: [
    "student loan calculator",
    "uk student loans",
    "plan 5 calculator",
    "loan repayments",
    "student finance",
    "loan comparison",
  ],
  creator: "Student Loan Calculator UK",
  publisher: "Student Loan Calculator UK",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://studentloancalculator.uk/",
  },
  formatDetection: {
    telephone: false,
    address: false,
    email: false,
  },
  openGraph: {
    title: "Student Loan Calculator UK - Understand Your Repayments",
    description: "Trusted tools, expert guides, and calculators to understand UK student loan repayments. Compare Plan 1, 2, 4, 5 and Postgraduate loans.",
    siteName: "Student Loan Calculator UK",
    locale: "en_GB",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Student Loan Calculator UK",
    description: "Calculate your UK student loan repayments with our free calculators.",
    images: ["http://localhost:3000/og-image.jpg"], // Update this to your production URL
  },
  icons: {
    icon: [
      { url: "/favicon.ico" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
    ],
    other: [{ rel: "mask-icon", url: "/safari-pinned-tab.svg" }],
  },
};

export default function Home() {
  const webPageSchema: WebPage = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name: "UK Student Loan Calculator Homepage",
    description: "Free UK student loan calculators and guides to help students and graduates understand their loan repayments.",
    url: "https://studentloancalculator.uk/",
    datePublished: "2025-05-09T05:42:41.382Z",
    dateModified: new Date().toISOString(),
  };

  const webSiteSchema: WebSite = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "Student Loan Calculator UK",
    url: "https://studentloancalculator.uk",
    description: "Trusted tools, expert guides, and calculators built for UK students and graduates.",
    potentialAction: {
      "@type": "SearchAction",
      target: {
        "@type": "EntryPoint",
        urlTemplate: "https://studentloancalculator.uk/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <>
      <JsonLd code={[webPageSchema, webSiteSchema]} />
      <div>
        <HomePage />
      </div>
    </>
  );
}